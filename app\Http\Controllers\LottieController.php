<?php
namespace App\Http\Controllers;

use App\Models\LottieFile;
use App\Mail\ShareLottieAnimation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class LottieController extends Controller
{
    public function index()
    {
        return view('welcome');
    }
    private function extractMetadata($file): array
    {
        // Use Laravel's Storage facade to safely read the file
        $content = $file->get();
        $json = json_decode($content, true);
        
        // Validate required Lottie structure
        if (!isset($json['v']) || !isset($json['layers'])) {
            throw new \InvalidArgumentException('Invalid Lottie file structure');
        }

        return [
            'width' => $json['w'] ?? null,
            'height' => $json['h'] ?? null,
            'frames' => $json['fr'] ?? null,
            'total_frames' => $json['op'] ?? null,
            'version' => $json['v'] ?? null,
            'assets' => count($json['assets'] ?? []),
            'layers' => count($json['layers'] ?? []),
        ];
    }
    public function upload(Request $request)
    {
        try {
            // Validate the request with stricter rules
            $request->validate([
                'lottie_file' => 'required|file|mimes:json,txt|max:5120', // Max 5MB, only JSON files
                '_token' => 'required'  // Require CSRF token
            ]);

            $file = $request->file('lottie_file');
            
            // Validate file extension
            $extension = strtolower($file->getClientOriginalExtension());
            if ($extension !== 'json') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only JSON files are allowed'
                ], 422);
            }

            // Validate JSON content safely
            try {
                $content = $file->get();
                $json = json_decode($content, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid JSON file format'
                    ], 422);
                }
                
                // Validate Lottie structure
                if (!isset($json['v']) || !isset($json['layers'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid Lottie file structure'
                    ], 422);
                }
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error processing file: ' . $e->getMessage()
                ], 422);
            }

            // Extract metadata
            $metadata = $this->extractMetadata($file);

            // Generate unique filename
            $fileName = Str::uuid() . '.json';
            $filePath = 'private/lottie/' . $fileName;

            // Store the file
            Storage::put($filePath, $content);

            // Create database record
            $lottieFile = LottieFile::create([
                'original_name' => $file->getClientOriginalName(),
                'file_name' => $fileName,
                'file_path' => $filePath,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'metadata' => $metadata,
                'is_valid' => true,
                'status' => 'active',
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Lottie file uploaded successfully',
                'share_url' => route('lottie.show', ['uuid' => $lottieFile->uuid]),
                'uuid' => $lottieFile->uuid,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a shared Lottie animation.
     */
    public function show($uuid)
    {
        $lottieFile = $this->getLottieFile($uuid);
        // Check if file exists in storage
        if (!Storage::exists($lottieFile->file_path)) {
            abort(404, 'Lottie file not found in storage');
        }
        // Get the file content to verify it's readable
        try {
            $content = Storage::get($lottieFile->file_path);
            $json = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                abort(400, 'Invalid JSON file');
            }
        } catch (\Exception $e) {
            // Log the error but don't expose details to user
            \Illuminate\Support\Facades\Log::error('Error reading Lottie file: ' . $e->getMessage());
            abort(500, 'Error reading Lottie file');
        }

        return view('lottie.show', compact('lottieFile'));
    }

    public function preview($uuid)
    {
        $lottieFile = $this->getLottieFile($uuid);

        if (!Storage::exists($lottieFile->file_path)) {
            abort(404, 'Lottie file not found');
        }
        
        try {
            $content = Storage::get($lottieFile->file_path);
            // Validate JSON before returning
            $json = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                abort(400, 'Invalid JSON file');
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error reading Lottie file: ' . $e->getMessage());
            abort(500, 'Error reading file');
        }

        return response($content)
            ->header('Content-Type', 'application/json')
            ->header('Content-Disposition', 'inline')
            ->header('X-Content-Type-Options', 'nosniff'); // Prevent MIME type sniffing
    }

    public function shareViaEmail(Request $request, $uuid)
    {
        // Check if user is authenticated
        // Removed, handled by middleware
        
        // Validate email and optional message
        $request->validate([
            'email' => 'required|email',
            'uuid' => 'required|string',
            'share_url' => 'required|url',
            'message' => 'nullable|string|max:500'
        ]);
        
        $lottieFile = $this->getLottieFile($uuid);
        $shareUrl = $request->share_url;
        
        // Rate limiting - check if user has sent too many emails recently
        $key = 'email_share_' . auth()->id();
        $maxEmails = 60; // Maximum emails per hour
        $currentCount = cache()->get($key, 0);
        
        if ($currentCount >= $maxEmails) {
            return response()->json([
                'success' => false,
                'message' => 'You have reached the maximum number of emails you can send per hour'
            ], 429); // Too Many Requests
        }
        
        // Increment the counter and set expiry
        cache()->put($key, $currentCount + 1, now()->addHour());

        // Send email using Laravel's Mail facade
        try {
            Mail::to($request->email)->send(
                new ShareLottieAnimation($lottieFile, $shareUrl, $request->message)
            );
            
            // Return a success response if email is sent successfully
            return response()->json(['success' => true, 'message' => 'Email sent successfully', 'url' => $shareUrl], 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error sending email: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error sending email'
            ], 500);
        }
    }
    public function getUserLottieFiles()
    {
        try {
            $lottieFiles = LottieFile::where('user_id', auth()->id())
                ->where('status', 'active')
                ->select([
                    'id',
                    'uuid',
                    'original_name',
                    'file_size',
                    'metadata',
                    'created_at'
                ])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($file) {
                    return [
                        'id' => $file->id,
                        'uuid' => $file->uuid,
                        'name' => $file->original_name,
                        'size' => $file->file_size,
                        'metadata' => $file->metadata,
                        'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                        'preview_url' => route('lottie.preview', ['uuid' => $file->uuid]),
                        'share_url' => route('lottie.show', ['uuid' => $file->uuid])
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $lottieFiles
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving Lottie files: ' . $e->getMessage()
            ], 500);
        }
    }

    public function myFiles()
    {
        return view('lottie.my-files');
    }

   
    public function download($uuid, $format = null)
    {
        // Check if user is authenticated
        // Removed, handled by middleware
        
        $lottieFile = $this->getLottieFile($uuid);
        
        if (!Storage::exists($lottieFile->file_path)) {
            abort(404, 'Lottie file not found');
        }
        
        $content = Storage::get($lottieFile->file_path);
        
        switch ($format) {
            case 'gif':
                return response()->json(['success' => false, 'comming soon' => true, 'message' => 'GIF conversion is not implemented in this demo'], 501);
            case 'mov':
                return response()->json(['success' => false, 'comming soon' => true, 'message' => 'MOV conversion is not implemented in this demo'], 501);
            case 'webm':
                // For demonstration, we'll return a message that WebM conversion is not implemented
                return response()->json([
                    'success' => false,
                    'comming soon' => true,
                    'message' => 'WebM conversion is not implemented in this demo'
                ], 501); // Not Implemented
                
            case 'json':
            default:
                return response($content)
                    ->header('Content-Type', 'application/json')
                    ->header('Content-Disposition', 'attachment; filename="' . $lottieFile->original_name . '"')
                    ->header('X-Content-Type-Options', 'nosniff');
        }
    }
    

    public function rename(Request $request, $uuid)
    {
        $request->validate([
            'name' => 'required|string|max:255'
        ]);
        
        $lottieFile = $this->getLottieFile($uuid);
        
        // Update the file name
        $lottieFile->original_name = $request->name;
        $lottieFile->save();
        
        return response()->json([
            'success' => true,
            'message' => 'File renamed successfully',
            'data' => [
                'name' => $lottieFile->original_name
            ]
        ]);
    }
    
    private function getLottieFile($uuid){
        $lottieFile = LottieFile::where('user_id', auth()->id())->where('uuid', $uuid)->firstOrFail();
        if (!$lottieFile) {
            abort(404, 'Lottie file not found');
        }
        return $lottieFile;
    }

    public function delete($uuid)
    {
        // Check if user is authenticated
        // Removed, handled by middleware
        
        $lottieFile = $this->getLottieFile($uuid);
        
        // Delete the file from storage
        if (Storage::exists($lottieFile->file_path)) {
            Storage::delete($lottieFile->file_path);
        }
        
        // Delete the database record
        $lottieFile->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }
}

