// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics, logEvent } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || null,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || null,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || null,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || null,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || null,
  appId: import.meta.env.VITE_FIREBASE_APP_ID || null,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || null
};

// Check if Firebase config is complete
const isFirebaseConfigComplete = () => {
  return firebaseConfig.apiKey && 
         firebaseConfig.authDomain && 
         firebaseConfig.projectId && 
         firebaseConfig.storageBucket && 
         firebaseConfig.messagingSenderId && 
         firebaseConfig.appId;
};

// Initialize Firebase services
let app = null;
let analytics = null;
let auth = null;
let db = null;
let storage = null;

// Initialize Firebase if config is complete
if (isFirebaseConfigComplete()) {
  try {
    app = initializeApp(firebaseConfig);
    auth = getAuth(app);
    db = getFirestore(app);
    storage = getStorage(app);
    console.log('Firebase initialized successfully');
  } catch (error) {
    console.error('Firebase initialization failed:', error);
  }
} else {
  console.warn('Firebase configuration incomplete. Please check your environment variables.');
  console.warn('Required variables: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID');
}

// Initialize analytics after DOM is ready
const initializeAnalytics = () => {
  if (typeof window !== 'undefined' && app && !analytics) {
    try {
      analytics = getAnalytics(app);
      // Log page view after analytics is initialized
      logEvent(analytics, 'page_view');
      console.log('Firebase Analytics initialized successfully');
    } catch (error) {
      console.warn('Firebase Analytics initialization failed:', error);
    }
  }
};

// Initialize analytics when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAnalytics);
  } else {
    initializeAnalytics();
  }
}

// Export the services for use in other parts of your app
export { app, analytics, auth, db, storage };

// Export logEvent function for use in other parts of your app
export { logEvent };
