<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LottieController;

Route::view('/', 'welcome')->name('index');

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

// Lottie Routes
// Route::get('/previewlottie', [LottieController::class, 'index'])->name('lottie.index');

// Protected Lottie Routes (require authentication)
Route::middleware(['auth'])->controller(LottieController::class)->group(function () {
    // i want to use the Lottiecontroller with middleware
    Route::post('/lottie/upload',  'upload')->name('lottie.upload');
    Route::post('/lottie/{uuid}/share-email',  'shareViaEmail')->name('lottie.share.email');
    Route::put('/lottie/rename/{uuid}',  'rename')->name('lottie.rename');
    Route::delete('/lottie/{uuid}',  'delete')->name('lottie.delete');
    Route::get('/lottie/download/{uuid}/{format?}',  'download')->name('lottie.download');
});

// Public Lottie Routes (accessible without authentication)
Route::get('/lottie/{uuid}', [LottieController::class, 'show'])->name('lottie.show');
Route::get('/lottie/preview/{uuid}', [LottieController::class, 'preview'])->name('lottie.preview');
Route::get("/payment", function () {
    return view("payment.payment");
});
Route::get("/receipt", function () {
    return view("payment.receipt");
});
Route::get("/guest", function () {
    return view("landing.landing");
});

// User's Lottie Files
Route::get('/user/lottie-files', [LottieController::class, 'getUserLottieFiles'])
    ->middleware('auth')
    ->name('lottie.user.files');

Route::get('/my-files', [LottieController::class, 'myFiles'])
    ->middleware('auth')
    ->name('lottie.my.files');

// Firebase Test Route (for development/testing)
Route::view('/firebase-test', 'firebase-test')
    ->middleware('auth')
    ->name('firebase.test');

require __DIR__.'/auth.php';
