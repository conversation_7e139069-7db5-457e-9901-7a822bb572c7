# Firebase Setup Guide

This project uses Firebase for authentication, database, storage, and analytics. Follow these steps to set up Firebase properly.

## 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or select an existing project
3. Follow the setup wizard

## 2. Enable Firebase Services

### Authentication
1. In Firebase Console, go to "Authentication" → "Sign-in method"
2. Enable "Email/Password" authentication
3. Optionally enable other providers as needed

### Firestore Database
1. Go to "Firestore Database" → "Create database"
2. Choose "Start in test mode" for development
3. Select a location for your database

### Storage
1. Go to "Storage" → "Get started"
2. Choose "Start in test mode" for development
3. Select a location for your storage

### Analytics
1. Go to "Analytics" → "Get started"
2. Follow the setup wizard

## 3. Get Firebase Configuration

1. In Firebase Console, go to Project Settings (gear icon)
2. Scroll down to "Your apps" section
3. Click "Add app" → "Web"
4. Register your app with a nickname
5. Copy the configuration object

## 4. Set Environment Variables

Create a `.env` file in your project root and add these variables:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

## 5. Security Rules

### Firestore Rules
Update your Firestore security rules in Firebase Console:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read/write lottie files
    match /lottie_files/{fileId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### Storage Rules
Update your Storage security rules:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload/download files
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 6. Test Firebase Integration

1. Start your development server: `php artisan serve`
2. Visit `/firebase-test` to test Firebase integration
3. Check the browser console for any errors

## 7. Troubleshooting

### Common Issues

1. **"Firebase not initialized" error**
   - Check that all environment variables are set correctly
   - Ensure the `.env` file is in the project root
   - Restart your development server after changing environment variables

2. **"logEvent is not a function" error**
   - This has been fixed in the updated code
   - Make sure to rebuild assets: `npm run build`

3. **Analytics not working**
   - Check that `VITE_FIREBASE_MEASUREMENT_ID` is set
   - Ensure Analytics is enabled in Firebase Console
   - Check browser console for any errors

4. **Authentication errors**
   - Verify Email/Password authentication is enabled in Firebase Console
   - Check that the domain is added to authorized domains

### Environment Variables Reference

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_FIREBASE_API_KEY` | Firebase API key | Yes |
| `VITE_FIREBASE_AUTH_DOMAIN` | Firebase auth domain | Yes |
| `VITE_FIREBASE_PROJECT_ID` | Firebase project ID | Yes |
| `VITE_FIREBASE_STORAGE_BUCKET` | Firebase storage bucket | Yes |
| `VITE_FIREBASE_MESSAGING_SENDER_ID` | Firebase messaging sender ID | Yes |
| `VITE_FIREBASE_APP_ID` | Firebase app ID | Yes |
| `VITE_FIREBASE_MEASUREMENT_ID` | Firebase analytics measurement ID | No (for analytics) |

## 8. Production Deployment

1. Update security rules for production
2. Set up proper authentication methods
3. Configure domain restrictions
4. Set up monitoring and alerts
5. Test thoroughly before going live

## 9. Firebase Services Used

- **Authentication**: User sign-in/sign-up
- **Firestore**: Database for storing lottie files and user data
- **Storage**: File storage for lottie animations
- **Analytics**: User behavior tracking and insights

## 10. Development vs Production

### Development
- Use test mode for Firestore and Storage
- Enable all authentication methods for testing
- Use development measurement ID

### Production
- Set up proper security rules
- Restrict authentication methods
- Use production measurement ID
- Set up monitoring and alerts 