@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Firebase Integration Test</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Firebase Status</h2>
            <div id="firebase-status" class="space-y-2">
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-gray-300 mr-2" id="auth-status"></span>
                    <span>Authentication</span>
                </div>
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-gray-300 mr-2" id="firestore-status"></span>
                    <span>Firestore Database</span>
                </div>
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-gray-300 mr-2" id="storage-status"></span>
                    <span>Storage</span>
                </div>
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-gray-300 mr-2" id="analytics-status"></span>
                    <span>Analytics</span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Authentication</h2>
            <form id="auth-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" id="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" id="password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>
                <div class="flex space-x-4">
                    <button type="button" id="signin-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Sign In</button>
                    <button type="button" id="signup-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Sign Up</button>
                    <button type="button" id="signout-btn" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">Sign Out</button>
                </div>
            </form>
            <div id="auth-result" class="mt-4 p-3 rounded hidden"></div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Firestore</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Collection Name</label>
                    <input type="text" id="collection-name" value="test" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Document Data (JSON)</label>
                    <textarea id="document-data" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" rows="3">{"name": "Test Document", "timestamp": "2024-01-01"}</textarea>
                </div>
                <div class="flex space-x-4">
                    <button type="button" id="add-doc-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Add Document</button>
                    <button type="button" id="get-docs-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Get Documents</button>
                </div>
            </div>
            <div id="firestore-result" class="mt-4 p-3 rounded hidden"></div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Test Analytics</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Event Name</label>
                    <input type="text" id="event-name" value="test_event" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Event Parameters (JSON)</label>
                    <textarea id="event-params" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" rows="3">{"test_param": "test_value"}</textarea>
                </div>
                <div class="flex space-x-4">
                    <button type="button" id="log-event-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Log Event</button>
                    <button type="button" id="log-pageview-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Log Page View</button>
                </div>
            </div>
            <div id="analytics-result" class="mt-4 p-3 rounded hidden"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check Firebase status
    function updateStatus() {
        const authStatus = document.getElementById('auth-status');
        const firestoreStatus = document.getElementById('firestore-status');
        const storageStatus = document.getElementById('storage-status');
        const analyticsStatus = document.getElementById('analytics-status');

        // Check if Firebase services are available
        if (window.firebaseAuth && window.firebaseDB && window.firebaseStorage && window.firebaseAnalytics) {
            authStatus.className = 'w-4 h-4 rounded-full bg-green-500 mr-2';
            firestoreStatus.className = 'w-4 h-4 rounded-full bg-green-500 mr-2';
            storageStatus.className = 'w-4 h-4 rounded-full bg-green-500 mr-2';
            
            if (window.firebaseAnalytics.isAvailable()) {
                analyticsStatus.className = 'w-4 h-4 rounded-full bg-green-500 mr-2';
            } else {
                analyticsStatus.className = 'w-4 h-4 rounded-full bg-yellow-500 mr-2';
            }
        } else {
            authStatus.className = 'w-4 h-4 rounded-full bg-red-500 mr-2';
            firestoreStatus.className = 'w-4 h-4 rounded-full bg-red-500 mr-2';
            storageStatus.className = 'w-4 h-4 rounded-full bg-red-500 mr-2';
            analyticsStatus.className = 'w-4 h-4 rounded-full bg-red-500 mr-2';
        }
    }

    // Update status after a short delay to allow Firebase to initialize
    setTimeout(updateStatus, 1000);

    // Authentication tests
    document.getElementById('signin-btn').addEventListener('click', async function() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const resultDiv = document.getElementById('auth-result');

        try {
            const result = await window.firebaseAuth.signIn(email, password);
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? 'Sign in successful!' : `Sign in failed: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });

    document.getElementById('signup-btn').addEventListener('click', async function() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const resultDiv = document.getElementById('auth-result');

        try {
            const result = await window.firebaseAuth.register(email, password);
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? 'Registration successful!' : `Registration failed: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });

    document.getElementById('signout-btn').addEventListener('click', async function() {
        const resultDiv = document.getElementById('auth-result');

        try {
            const result = await window.firebaseAuth.signOut();
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? 'Sign out successful!' : `Sign out failed: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });

    // Firestore tests
    document.getElementById('add-doc-btn').addEventListener('click', async function() {
        const collectionName = document.getElementById('collection-name').value;
        const documentData = JSON.parse(document.getElementById('document-data').value);
        const resultDiv = document.getElementById('firestore-result');

        try {
            const result = await window.firebaseDB.addDocument(collectionName, documentData);
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? `Document added with ID: ${result.id}` : `Failed to add document: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });

    document.getElementById('get-docs-btn').addEventListener('click', async function() {
        const collectionName = document.getElementById('collection-name').value;
        const resultDiv = document.getElementById('firestore-result');

        try {
            const result = await window.firebaseDB.getDocuments(collectionName);
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? `Found ${result.data.length} documents: ${JSON.stringify(result.data, null, 2)}` : `Failed to get documents: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });

    // Analytics tests
    document.getElementById('log-event-btn').addEventListener('click', async function() {
        const eventName = document.getElementById('event-name').value;
        const eventParams = JSON.parse(document.getElementById('event-params').value);
        const resultDiv = document.getElementById('analytics-result');

        try {
            const result = await window.firebaseAnalytics.logEvent(eventName, eventParams);
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? 'Event logged successfully!' : `Failed to log event: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });

    document.getElementById('log-pageview-btn').addEventListener('click', async function() {
        const resultDiv = document.getElementById('analytics-result');

        try {
            const result = await window.firebaseAnalytics.logPageView('test-page');
            resultDiv.className = result.success ? 'mt-4 p-3 rounded bg-green-100 text-green-800' : 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = result.success ? 'Page view logged successfully!' : `Failed to log page view: ${result.error}`;
            resultDiv.classList.remove('hidden');
        } catch (error) {
            resultDiv.className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
            resultDiv.textContent = `Error: ${error.message}`;
            resultDiv.classList.remove('hidden');
        }
    });
});
</script>
@endsection
