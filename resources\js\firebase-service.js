// Firebase service functions
import { auth, db, storage, analytics, logEvent } from './firebase';
import { 
    signInWithEmailAndPassword, 
    createUserWithEmailAndPassword, 
    signOut,
    onAuthStateChanged 
} from 'firebase/auth';
import { 
    collection, 
    addDoc, 
    getDocs, 
    doc, 
    updateDoc, 
    deleteDoc 
} from 'firebase/firestore';
import { 
    ref, 
    uploadBytes, 
    getDownloadURL, 
    deleteObject 
} from 'firebase/storage';

// Helper function to check if Firebase is initialized
const isFirebaseInitialized = () => {
    return auth !== null && db !== null && storage !== null;
};

// Authentication functions
export const firebaseAuth = {
    // Sign in user
    signIn: async (email, password) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            return { success: true, user: userCredential.user };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Register new user
    register: async (email, password) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            return { success: true, user: userCredential.user };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Sign out user
    signOut: async () => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            await signOut(auth);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Listen to auth state changes
    onAuthStateChanged: (callback) => {
        if (!isFirebaseInitialized()) {
            console.warn('Firebase not initialized, auth state listener not available');
            return () => {};
        }
        return onAuthStateChanged(auth, callback);
    },

    // Get current user
    getCurrentUser: () => {
        if (!isFirebaseInitialized()) {
            return null;
        }
        return auth.currentUser;
    }
};

// Firestore database functions
export const firebaseDB = {
    // Add document to collection
    addDocument: async (collectionName, data) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const docRef = await addDoc(collection(db, collectionName), data);
            return { success: true, id: docRef.id };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Get all documents from collection
    getDocuments: async (collectionName) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const querySnapshot = await getDocs(collection(db, collectionName));
            const documents = [];
            querySnapshot.forEach((doc) => {
                documents.push({ id: doc.id, ...doc.data() });
            });
            return { success: true, data: documents };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Update document
    updateDocument: async (collectionName, docId, data) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const docRef = doc(db, collectionName, docId);
            await updateDoc(docRef, data);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Delete document
    deleteDocument: async (collectionName, docId) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            await deleteDoc(doc(db, collectionName, docId));
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
};

// Firebase Storage functions
export const firebaseStorage = {
    // Upload file
    uploadFile: async (file, path) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const storageRef = ref(storage, path);
            const snapshot = await uploadBytes(storageRef, file);
            const downloadURL = await getDownloadURL(snapshot.ref);
            return { success: true, url: downloadURL, path: path };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Get download URL
    getDownloadURL: async (path) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            const url = await getDownloadURL(ref(storage, path));
            return { success: true, url: url };
        } catch (error) {
            return { success: false, error: error.message };
        }
    },

    // Delete file
    deleteFile: async (path) => {
        if (!isFirebaseInitialized()) {
            return { success: false, error: 'Firebase not initialized' };
        }
        try {
            await deleteObject(ref(storage, path));
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
};

// Analytics functions
export const firebaseAnalytics = {
    // Log custom event
    logEvent: async (eventName, parameters = {}) => {
        try {
            // Wait for analytics to be initialized
            let attempts = 0;
            const maxAttempts = 10;
            
            while (!analytics && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (analytics) {
                await logEvent(analytics, eventName, parameters);
                return { success: true };
            } else {
                console.warn('Firebase Analytics not initialized after waiting');
                return { success: false, error: 'Analytics not initialized' };
            }
        } catch (error) {
            console.error('Analytics error:', error);
            return { success: false, error: error.message };
        }
    },

    // Log page view
    logPageView: async (pageName) => {
        return firebaseAnalytics.logEvent('page_view', { page_name: pageName });
    },

    // Log user action
    logUserAction: async (action, parameters = {}) => {
        return firebaseAnalytics.logEvent('user_action', { action, ...parameters });
    },

    // Check if analytics is available
    isAvailable: () => {
        return analytics !== null;
    }
};

// Make services available globally for easy access
window.firebaseAuth = firebaseAuth;
window.firebaseDB = firebaseDB;
window.firebaseStorage = firebaseStorage;
window.firebaseAnalytics = firebaseAnalytics;
