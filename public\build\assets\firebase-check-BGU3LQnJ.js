const i={BASE_URL:"/build/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_APP_NAME:"ShowLottie",VITE_FIREBASE_API_KEY:"AIzaSyBN68eIVPPp6S7hAIXZujXmj-0v87S0KA8",VITE_FIREBASE_APP_ID:"1:29289879674:web:dc10ec62966f9be1e583c3",VITE_FIREBASE_AUTH_DOMAIN:"lottie-cfb42.firebaseapp.com",VITE_FIREBASE_MEASUREMENT_ID:"G-PE7G8QLBWH",VITE_FIREBASE_MESSAGING_SENDER_ID:"29289879674",VITE_FIREBASE_PROJECT_ID:"lottie-cfb42",VITE_FIREBASE_STORAGE_BUCKET:"lottie-cfb42.firebasestorage.app"};console.log("🔍 Checking Firebase Configuration...");const t=()=>{const s=["VITE_FIREBASE_API_KEY","VITE_FIREBASE_AUTH_DOMAIN","VITE_FIREBASE_PROJECT_ID","VITE_FIREBASE_STORAGE_BUCKET","VITE_FIREBASE_MESSAGING_SENDER_ID","VITE_FIREBASE_APP_ID"],n=["VITE_FIREBASE_MEASUREMENT_ID"];console.log(`
📋 Environment Variables Check:`);let o=!0;return s.forEach(e=>{const r=i[e];r?console.log(`✅ ${e}: ${r.substring(0,10)}...`):(console.log(`❌ ${e}: Missing`),o=!1)}),n.forEach(e=>{const r=i[e];console.log(r?`✅ ${e}: ${r.substring(0,10)}...`:`⚠️  ${e}: Missing (optional for analytics)`)}),o},c=()=>{if(console.log(`
🔧 Firebase Services Check:`),typeof window<"u"){const s={"Firebase Auth":window.firebaseAuth,"Firebase DB":window.firebaseDB,"Firebase Storage":window.firebaseStorage,"Firebase Analytics":window.firebaseAnalytics};Object.entries(s).forEach(([n,o])=>{console.log(o?`✅ ${n}: Available`:`❌ ${n}: Not available`)})}else console.log("⚠️  Not in browser environment")},E=()=>{console.log(`
🚨 Error Check:`);const s=console.error,n=[];console.error=(...o)=>{const e=o.join(" ");(e.includes("firebase")||e.includes("Firebase"))&&n.push(e),s.apply(console,o)},setTimeout(()=>{n.length>0?(console.log("❌ Firebase errors detected:"),n.forEach(o=>{console.log(`   - ${o}`)})):console.log("✅ No Firebase errors detected"),console.error=s},2e3)},l=()=>{console.log(`🚀 Starting Firebase Configuration Check...
`);const s=t();c(),E(),setTimeout(()=>{console.log(`
📊 Summary:`),s?console.log("✅ Environment variables are properly configured"):(console.log("❌ Missing required environment variables"),console.log("💡 Please check your .env file and ensure all required Firebase variables are set")),console.log(`
🔧 Next Steps:`),console.log("1. If environment variables are missing, add them to your .env file"),console.log("2. Restart your development server after changing .env"),console.log('3. Run "npm run build" to rebuild assets'),console.log("4. Visit /firebase-test to test the integration")},3e3)};typeof window<"u"&&(document.readyState==="loading"?document.addEventListener("DOMContentLoaded",l):l());
