// reCAPTCHA v3 Integration
document.addEventListener('DOMContentLoaded', function() {
    // Function to execute reCAP<PERSON><PERSON> before form submission
    window.executeRecaptchaOnSubmit = function(formElement, action = 'submit') {
        return new Promise((resolve, reject) => {
            // Check if reCAP<PERSON><PERSON> is loaded
            if (typeof grecaptcha === 'undefined') {
                console.error('reCAPTCHA not loaded');
                reject(new Error('reCAPTCHA not loaded'));
                return;
            }

            // Get the site key from the form or a data attribute
            const siteKey = formElement.dataset.recaptchaSiteKey || 
                           document.querySelector('[data-recaptcha-site-key]')?.dataset.recaptchaSiteKey;

            if (!siteKey) {
                console.error('reCAPTCHA site key not found');
                reject(new Error('reCAPTCHA site key not found'));
                return;
            }

            // Execute reCAPTCHA
            grecaptcha.execute(siteKey, { action: action })
                .then(token => {
                    // Add the token to the form
                    let tokenInput = formElement.querySelector('input[name="g-recaptcha-response"]');
                    if (!tokenInput) {
                        tokenInput = document.createElement('input');
                        tokenInput.type = 'hidden';
                        tokenInput.name = 'g-recaptcha-response';
                        formElement.appendChild(tokenInput);
                    }
                    tokenInput.value = token;
                    
                    // Dispatch event for Livewire
                    window.dispatchEvent(new CustomEvent('recaptcha-token-received', {
                        detail: { token: token }
                    }));
                    
                    resolve(token);
                })
                .catch(error => {
                    console.error('reCAPTCHA execution failed:', error);
                    reject(error);
                });
        });
    };

    // Auto-execute reCAPTCHA on form submission
    document.addEventListener('submit', function(event) {
        const form = event.target;
        
        // Check if form has reCAPTCHA enabled
        if (form.dataset.recaptchaEnabled === 'true') {
            event.preventDefault();
            
            const action = form.dataset.recaptchaAction || 'submit';
            
            executeRecaptchaOnSubmit(form, action)
                .then(() => {
                    // Re-submit the form after getting the token
                    form.submit();
                })
                .catch(error => {
                    console.error('reCAPTCHA failed:', error);
                    // Show error to user
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'text-red-600 text-sm mt-2';
                    errorDiv.textContent = 'Security verification failed. Please try again.';
                    
                    const existingError = form.querySelector('.text-red-600');
                    if (existingError) {
                        existingError.remove();
                    }
                    
                    form.appendChild(errorDiv);
                });
        }
    });

    // Function to manually execute reCAPTCHA
    window.executeRecaptcha = function(action = 'submit') {
        const form = document.querySelector('[data-recaptcha-enabled="true"]');
        if (form) {
            return executeRecaptchaOnSubmit(form, action);
        }
        return Promise.reject(new Error('No reCAPTCHA enabled form found'));
    };
}); 