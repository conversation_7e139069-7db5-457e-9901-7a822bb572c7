name: Deploy to Namecheap Shared Hosting (SSH)

on:
  push:
    branches:
      - master
  workflow_dispatch: # Allow manual deployment

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 15 # Prevent hanging deployments
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Full history for better caching

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, dom, filter, gd, iconv, json, mbstring, pdo, phar, fileinfo, openssl, tokenizer, xml, curl, zip
          coverage: none

      - name: Cache Composer dependencies
        uses: actions/cache@v3
        with:
          path: vendor
          key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-php-

      - name: Install Composer dependencies
        run: composer install --no-dev --optimize-autoloader --no-interaction
        if: steps.cache.outputs.cache-hit != 'true'

      - name: Set up SSH key
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Add SSH host to known_hosts
        run: |
          ssh-keyscan -p 21098 -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Check server directory structure
        run: |
          ssh -p 21098 ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_HOST }} '
            echo "Current directory: $(pwd)"
            echo "Home directory contents:"
            ls -la ~
            echo "Checking if lottie.yourailist.com exists:"
            if [ -d "lottie.yourailist.com" ]; then
              echo "Directory exists"
              ls -la lottie.yourailist.com/
            else
              echo "Directory does not exist"
            fi
          '

      - name: Create deployment script
        run: |
          cat > deploy.sh << 'EOF'
          #!/bin/bash
          set -euo pipefail
          
          # Define the target directory
          TARGET_DIR="lottie.yourailist.com"
          
          echo "Starting deployment process..."
          echo "Current directory: $(pwd)"
          
          # Check if directory exists, create if it doesn't
          if [ ! -d "$TARGET_DIR" ]; then
            echo "Creating directory: $TARGET_DIR"
            mkdir -p "$TARGET_DIR"
          else
            echo "Directory $TARGET_DIR already exists"
          fi
          
          cd "$TARGET_DIR"
          echo "Changed to directory: $(pwd)"
          
          # Check if Laravel files exist
          if [ ! -f "artisan" ]; then
            echo "Error: Laravel artisan file not found. Deployment may have failed."
            exit 1
          fi
          
          # Backup current .env
          if [ -f .env ]; then
            echo "Backing up .env file"
            cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
          else
            echo "No .env file found to backup"
          fi
          
          # Install dependencies
          echo "Installing Composer dependencies..."
          composer install --no-dev --optimize-autoloader --no-interaction
          
          # Run migrations with rollback capability
          echo "Running database migrations..."
          php artisan migrate --force
          
          # Clear and cache configurations
          echo "Caching configurations..."
          php artisan config:clear
          php artisan config:cache
          
          # Clear and cache routes
          echo "Caching routes..."
          php artisan route:clear
          php artisan route:cache
          
          # Clear and cache views
          echo "Caching views..."
          php artisan view:clear
          php artisan view:cache
          
          # Create storage link if it doesn't exist
          if [ ! -L public/storage ]; then
            echo "Creating storage link..."
            php artisan storage:link
          else
            echo "Storage link already exists"
          fi
          
          # Run seeders only if needed (comment out if not required for production)
          # php artisan db:seed --force
          
          # Clear application cache
          echo "Clearing application cache..."
          php artisan cache:clear
          
          # Optimize for production
          echo "Optimizing for production..."
          php artisan optimize
          
          echo "Deployment completed successfully!"
          EOF
          chmod +x deploy.sh

      - name: Copy files via SSH (rsync)
        run: |
          rsync -avz -e "ssh -p 21098" --delete \
            --exclude='.env' \
            --exclude='storage/' \
            --exclude='vendor/' \
            --exclude='node_modules/' \
            --exclude='.git/' \
            --exclude='tests/' \
            --exclude='.github/' \
            --exclude='*.log' \
            --exclude='.env.example' \
            --exclude='composer.lock' \
            --exclude='package-lock.json' \
            ./ ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_HOST }}:lottie.yourailist.com/

      - name: Copy deployment script
        run: |
          scp -P 21098 deploy.sh ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_HOST }}:~

      - name: Run deployment script
        run: |
          ssh -p 21098 ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_HOST }} '
            cd ~ &&
            chmod +x deploy.sh &&
            ./deploy.sh
          '

      - name: Health check
        run: |
          curl -f -s -o /dev/null -w "%{http_code}" \
            https://lottie.yourailist.com || exit 1
        continue-on-error: true

      - name: Cleanup deployment script
        run: |
          ssh -p 21098 ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_HOST }} '
            cd lottie.yourailist.com &&
            rm -f deploy.sh
          '
        if: always() 