import{a as n,d as i,s as u,b as c}from"./firebase-xVbGI4Uy.js";import{o as y,s as m,c as b,a as h,d as F,b as f,u as z,g as A,e as g,f as p,h as D,r as o,i as w,j as R,l as U}from"./index.esm2017-Bi9TsIr0.js";const t=()=>n!==null&&i!==null&&u!==null,v={signIn:async(e,r)=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,user:(await h(n,e,r)).user}}catch(s){return{success:!1,error:s.message}}},register:async(e,r)=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,user:(await b(n,e,r)).user}}catch(s){return{success:!1,error:s.message}}},signOut:async()=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return await m(n),{success:!0}}catch(e){return{success:!1,error:e.message}}},onAuthStateChanged:e=>t()?y(n,e):(console.warn("Firebase not initialized, auth state listener not available"),()=>{}),getCurrentUser:()=>t()?n.currentUser:null},C={addDocument:async(e,r)=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,id:(await p(g(i,e),r)).id}}catch(s){return{success:!1,error:s.message}}},getDocuments:async e=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{const r=await A(g(i,e)),s=[];return r.forEach(a=>{s.push({id:a.id,...a.data()})}),{success:!0,data:s}}catch(r){return{success:!1,error:r.message}}},updateDocument:async(e,r,s)=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{const a=f(i,e,r);return await z(a,s),{success:!0}}catch(a){return{success:!1,error:a.message}}},deleteDocument:async(e,r)=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return await F(f(i,e,r)),{success:!0}}catch(s){return{success:!1,error:s.message}}}},E={uploadFile:async(e,r)=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{const s=o(u,r),a=await R(s,e);return{success:!0,url:await w(a.ref),path:r}}catch(s){return{success:!1,error:s.message}}},getDownloadURL:async e=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,url:await w(o(u,e))}}catch(r){return{success:!1,error:r.message}}},deleteFile:async e=>{if(!t())return{success:!1,error:"Firebase not initialized"};try{return await D(o(u,e)),{success:!0}}catch(r){return{success:!1,error:r.message}}}},l={logEvent:async(e,r={})=>{try{let s=0;const a=10;for(;!c&&s<a;)await new Promise(d=>setTimeout(d,100)),s++;return c?(await U(c,e,r),{success:!0}):(console.warn("Firebase Analytics not initialized after waiting"),{success:!1,error:"Analytics not initialized"})}catch(s){return console.error("Analytics error:",s),{success:!1,error:s.message}}},logPageView:async e=>l.logEvent("page_view",{page_name:e}),logUserAction:async(e,r={})=>l.logEvent("user_action",{action:e,...r}),isAvailable:()=>c!==null};window.firebaseAuth=v;window.firebaseDB=C;window.firebaseStorage=E;window.firebaseAnalytics=l;
