<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">{{ $lottieFile->original_name }}</h3>
                        <div class="flex items-center space-x-4">
                            <a href="{{ route('index') }}" class="text-gray-600 hover:text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row gap-8">
                        <!-- Lottie Preview -->
                        <div class="w-full md:w-2/3">
                            <div id="lottie-container" class="bg-gray-100 rounded-lg p-4 flex items-center justify-center" style="min-height: 400px;">
                                @if(Storage::exists($lottieFile->file_path))
                                    <lottie-player
                                        src="{{ route('lottie.preview', ['uuid' => $lottieFile->uuid]) }}"
                                        background="transparent"
                                        speed="1"
                                        style="width: 100%; height: 100%;"
                                        loop
                                        autoplay>
                                    </lottie-player>
                                @else
                                    <div class="text-red-500">
                                        Error: File not found at {{ $lottieFile->file_path }}
                                    </div>
                                @endif
                            </div>

                            <!-- Debug Information -->
                            @if(config('app.debug'))
                                <div class="mt-4 p-4 bg-gray-100 rounded-lg">
                                    <h4 class="font-semibold mb-2">Debug Information:</h4>
                                    <p>File Path: {{ $lottieFile->file_path }}</p>
                                    <p>Storage URL: {{ Storage::url($lottieFile->file_path) }}</p>
                                    <p>File Exists: {{ Storage::exists($lottieFile->file_path) ? 'Yes' : 'No' }}</p>
                                </div>
                            @endif

                            <!-- Playback Controls -->
                            <div class="mt-4 flex flex-wrap items-center gap-2">
                                <button id="play" class="btn-primary px-3 py-1 rounded-md text-sm">Play</button>
                                <button id="pause" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Pause</button>
                                <button id="stop" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Stop</button>
                                <div class="flex items-center gap-2 ml-4">
                                    <button class="speed-btn bg-black text-white px-2 py-1 rounded text-sm" data-speed="0.25">0.25x</button>
                                    <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="0.5">0.5x</button>
                                    <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="1">1x</button>
                                    <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="1.5">1.5x</button>
                                    <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="2">2x</button>
                                </div>
                            </div>
                        </div>

                        <!-- Animation Info -->
                        <div class="w-full md:w-1/3">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold mb-4">Animation Details</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Dimensions</label>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ $lottieFile->metadata['width'] ?? 'N/A' }} x {{ $lottieFile->metadata['height'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Frames</label>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ $lottieFile->metadata['frames'] ?? 'N/A' }} fps
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Total Frames</label>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ $lottieFile->metadata['total_frames'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Version</label>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ $lottieFile->metadata['version'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Assets</label>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ $lottieFile->metadata['assets'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Layers</label>
                                        <p class="mt-1 text-sm text-gray-600">
                                            {{ $lottieFile->metadata['layers'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Universal copy to clipboard function with fallback
        // function copyToClipboard(text, successCallback, errorCallback) {
        //     // Modern browsers with HTTPS
        //     if (navigator.clipboard && window.isSecureContext) {
        //         navigator.clipboard.writeText(text).then(() => {
        //             if (successCallback) successCallback();
        //         }).catch((err) => {
        //             console.error('Clipboard API failed:', err);
        //             fallbackCopyTextToClipboard(text, successCallback, errorCallback);
        //         });
        //     } else {
        //         // Fallback for older browsers or non-HTTPS
        //         fallbackCopyTextToClipboard(text, successCallback, errorCallback);
        //     }
        // }

        // function fallbackCopyTextToClipboard(text, successCallback, errorCallback) {
        //     const textArea = document.createElement("textarea");
        //     textArea.value = text;

        //     // Avoid scrolling to bottom
        //     textArea.style.top = "0";
        //     textArea.style.left = "0";
        //     textArea.style.position = "fixed";
        //     textArea.style.opacity = "0";

        //     document.body.appendChild(textArea);
        //     textArea.focus();
        //     textArea.select();

        //     try {
        //         const successful = document.execCommand('copy');
        //         if (successful && successCallback) {
        //             successCallback();
        //         } else if (!successful && errorCallback) {
        //             errorCallback();
        //         }
        //     } catch (err) {
        //         console.error('Fallback copy failed:', err);
        //         if (errorCallback) errorCallback();
        //     }

        //     document.body.removeChild(textArea);
        // }

        document.addEventListener('DOMContentLoaded', function() {
            const lottiePlayer = document.querySelector('lottie-player');
            const playBtn = document.getElementById('play');
            const pauseBtn = document.getElementById('pause');
            const stopBtn = document.getElementById('stop');
            const speedBtns = document.querySelectorAll('.speed-btn');
            // const copyLinkBtn = document.getElementById('copy-link');

            // Player controls
            playBtn.addEventListener('click', () => {
                lottiePlayer.play();
            });

            pauseBtn.addEventListener('click', () => {
                lottiePlayer.pause();
            });

            stopBtn.addEventListener('click', () => {
                lottiePlayer.stop();
            });

            // Speed controls
            speedBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const speed = parseFloat(btn.getAttribute('data-speed'));
                    lottiePlayer.speed = speed;

                    // Update active button
                    speedBtns.forEach(b => {
                        b.classList.remove('bg-black', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    btn.classList.remove('bg-gray-200', 'text-gray-700');
                    btn.classList.add('bg-black', 'text-white');
                });
            });
        });
    </script>
</x-app-layout>