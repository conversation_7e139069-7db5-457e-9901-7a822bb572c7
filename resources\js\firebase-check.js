// Firebase Configuration Check Script
// This script helps diagnose Firebase configuration issues

console.log('🔍 Checking Firebase Configuration...');

// Check if environment variables are available
const checkEnvironmentVariables = () => {
    const requiredVars = [
        'VITE_FIREBASE_API_KEY',
        'VITE_FIREBASE_AUTH_DOMAIN', 
        'VITE_FIREBASE_PROJECT_ID',
        'VITE_FIREBASE_STORAGE_BUCKET',
        'VITE_FIREBASE_MESSAGING_SENDER_ID',
        'VITE_FIREBASE_APP_ID'
    ];

    const optionalVars = [
        'VITE_FIREBASE_MEASUREMENT_ID'
    ];

    console.log('\n📋 Environment Variables Check:');
    
    let allRequiredPresent = true;
    requiredVars.forEach(varName => {
        const value = import.meta.env[varName];
        if (value) {
            console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
        } else {
            console.log(`❌ ${varName}: Missing`);
            allRequiredPresent = false;
        }
    });

    optionalVars.forEach(varName => {
        const value = import.meta.env[varName];
        if (value) {
            console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
        } else {
            console.log(`⚠️  ${varName}: Missing (optional for analytics)`);
        }
    });

    return allRequiredPresent;
};

// Check if Firebase services are available
const checkFirebaseServices = () => {
    console.log('\n🔧 Firebase Services Check:');
    
    if (typeof window !== 'undefined') {
        // Check if services are available globally
        const services = {
            'Firebase Auth': window.firebaseAuth,
            'Firebase DB': window.firebaseDB,
            'Firebase Storage': window.firebaseStorage,
            'Firebase Analytics': window.firebaseAnalytics
        };

        Object.entries(services).forEach(([name, service]) => {
            if (service) {
                console.log(`✅ ${name}: Available`);
            } else {
                console.log(`❌ ${name}: Not available`);
            }
        });
    } else {
        console.log('⚠️  Not in browser environment');
    }
};

// Check for common errors
const checkForErrors = () => {
    console.log('\n🚨 Error Check:');
    
    // Check if there are any console errors related to Firebase
    const originalError = console.error;
    const firebaseErrors = [];
    
    console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('firebase') || message.includes('Firebase')) {
            firebaseErrors.push(message);
        }
        originalError.apply(console, args);
    };

    // Wait a bit and then check for errors
    setTimeout(() => {
        if (firebaseErrors.length > 0) {
            console.log('❌ Firebase errors detected:');
            firebaseErrors.forEach(error => {
                console.log(`   - ${error}`);
            });
        } else {
            console.log('✅ No Firebase errors detected');
        }
        
        // Restore original console.error
        console.error = originalError;
    }, 2000);
};

// Run all checks
const runFirebaseCheck = () => {
    console.log('🚀 Starting Firebase Configuration Check...\n');
    
    const envVarsOk = checkEnvironmentVariables();
    checkFirebaseServices();
    checkForErrors();
    
    setTimeout(() => {
        console.log('\n📊 Summary:');
        if (envVarsOk) {
            console.log('✅ Environment variables are properly configured');
        } else {
            console.log('❌ Missing required environment variables');
            console.log('💡 Please check your .env file and ensure all required Firebase variables are set');
        }
        
        console.log('\n🔧 Next Steps:');
        console.log('1. If environment variables are missing, add them to your .env file');
        console.log('2. Restart your development server after changing .env');
        console.log('3. Run "npm run build" to rebuild assets');
        console.log('4. Visit /firebase-test to test the integration');
    }, 3000);
};

// Auto-run if this script is loaded
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runFirebaseCheck);
    } else {
        runFirebaseCheck();
    }
}

// Export for manual use
export { runFirebaseCheck }; 