<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Volt\Volt;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_login_screen_can_be_rendered(): void
    {
        $response = $this->get('/login');

        $response
            ->assertOk()
            ->assertSeeVolt('pages.auth.login');
    }

    public function test_users_can_authenticate_using_the_login_screen(): void
    {
        $user = User::factory()->create();

        // Mock the reCAPTCHA v2 response
        $this->app['request']->merge(['g-recaptcha-response' => 'test-token']);

        $component = Volt::test('pages.auth.login')
            ->set('form.email', $user->email)
            ->set('form.password', 'password');

        // Mock the file_get_contents call for reCAPTCHA verification
        $this->mock('alias:file_get_contents', function () {
            return json_encode(['success' => true]);
        });

        $component->call('login');

        $component
            ->assertHasNoErrors()
            ->assertRedirect(route('index', absolute: false));

        $this->assertAuthenticated();
    }

    public function test_users_can_not_authenticate_with_invalid_password(): void
    {
        $user = User::factory()->create();

        // Mock the reCAPTCHA v2 response
        $this->app['request']->merge(['g-recaptcha-response' => 'test-token']);

        $component = Volt::test('pages.auth.login')
            ->set('form.email', $user->email)
            ->set('form.password', 'wrong-password');

        // Mock the file_get_contents call for reCAPTCHA verification
        $this->mock('alias:file_get_contents', function () {
            return json_encode(['success' => true]);
        });

        $component->call('login');

        $component
            ->assertHasErrors()
            ->assertNoRedirect();

        $this->assertGuest();
    }

    public function test_navigation_menu_can_be_rendered(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);

        $response = $this->get('/index');

        $response
            ->assertOk()
            ->assertSeeVolt('layout.navigation');
    }

    public function test_users_can_logout(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);

        $component = Volt::test('layout.navigation');

        $component->call('logout');

        $component
            ->assertHasNoErrors()
            ->assertRedirect('/');

        $this->assertGuest();
    }

    public function test_login_fails_with_invalid_recaptcha(): void
    {
        $user = User::factory()->create();

        // Mock the reCAPTCHA v2 response with invalid token
        $this->app['request']->merge(['g-recaptcha-response' => 'invalid-token']);

        $component = Volt::test('pages.auth.login')
            ->set('form.email', $user->email)
            ->set('form.password', 'password');

        // Mock the file_get_contents call for reCAPTCHA verification to return failure
        $this->mock('alias:file_get_contents', function () {
            return json_encode(['success' => false]);
        });

        $component->call('login');

        $component
            ->assertHasErrors(['form.recaptchaToken'])
            ->assertNoRedirect();

        $this->assertGuest();
    }
}
