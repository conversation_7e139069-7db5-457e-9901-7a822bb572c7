<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Volt\Volt;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_login_screen_can_be_rendered(): void
    {
        $response = $this->get('/login');

        $response
            ->assertOk()
            ->assertSeeVolt('pages.auth.login');
    }

    public function test_users_can_authenticate_using_the_login_screen(): void
    {
        // Mock reCAPTCHA service to return success
        $this->mock(\App\Services\GoogleRecaptchaService::class, function ($mock) {
            $mock->shouldReceive('verify')->andReturn([
                'success' => true,
                'score' => 0.9,
                'action' => 'submit',
                'challenge_ts' => now()->toISOString(),
                'hostname' => 'localhost',
                'error_codes' => []
            ]);
            $mock->shouldReceive('isScoreAcceptable')->andReturn(true);
        });

        $user = User::factory()->create();

        $component = Volt::test('pages.auth.login')
            ->set('form.email', $user->email)
            ->set('form.password', 'password')
            ->set('form.recaptchaToken', 'test-token');

        $component->call('login');

        $component
            ->assertHasNoErrors()
            ->assertRedirect(route('index', absolute: false));

        $this->assertAuthenticated();
    }

    public function test_users_can_not_authenticate_with_invalid_password(): void
    {
        // Mock reCAPTCHA service to return success
        $this->mock(\App\Services\GoogleRecaptchaService::class, function ($mock) {
            $mock->shouldReceive('verify')->andReturn([
                'success' => true,
                'score' => 0.9,
                'action' => 'submit',
                'challenge_ts' => now()->toISOString(),
                'hostname' => 'localhost',
                'error_codes' => []
            ]);
            $mock->shouldReceive('isScoreAcceptable')->andReturn(true);
        });

        $user = User::factory()->create();

        $component = Volt::test('pages.auth.login')
            ->set('form.email', $user->email)
            ->set('form.password', 'wrong-password')
            ->set('form.recaptchaToken', 'test-token');

        $component->call('login');

        $component
            ->assertHasErrors()
            ->assertNoRedirect();

        $this->assertGuest();
    }

    public function test_navigation_menu_can_be_rendered(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);

        $response = $this->get('/index');

        $response
            ->assertOk()
            ->assertSeeVolt('layout.navigation');
    }

    public function test_users_can_logout(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user);

        $component = Volt::test('layout.navigation');

        $component->call('logout');

        $component
            ->assertHasNoErrors()
            ->assertRedirect('/');

        $this->assertGuest();
    }

    public function test_login_fails_with_invalid_recaptcha(): void
    {
        // Mock reCAPTCHA service to return failure
        $this->mock(\App\Services\GoogleRecaptchaService::class, function ($mock) {
            $mock->shouldReceive('verify')->andReturn([
                'success' => false,
                'score' => 0.0,
                'action' => 'submit',
                'challenge_ts' => now()->toISOString(),
                'hostname' => 'localhost',
                'error_codes' => ['invalid-input-response']
            ]);
        });

        $user = User::factory()->create();

        $component = Volt::test('pages.auth.login')
            ->set('form.email', $user->email)
            ->set('form.password', 'password')
            ->set('form.recaptchaToken', 'invalid-token');

        $component->call('login');

        $component
            ->assertHasErrors(['form.recaptchaToken'])
            ->assertNoRedirect();

        $this->assertGuest();
    }
}
