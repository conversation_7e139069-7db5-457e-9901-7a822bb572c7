<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WGEXML4H35"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-WGEXML4H35');

        </script>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="google-site-verification" content="vxMCyIWc_5bcIkgsjzruErrpUcEoNPz-gZ56zo9Ncxw" />

        <title>{{ config('app.name', 'ShowLottie') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700,800" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/css/lottie-theme.css', 'resources/js/app.js'
            , 'resources/js/common.js', 'resources/js/lottie-player.js', 'resources/js/firebase.js'])

        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        @livewireStyles
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            <livewire:layout.navigation />

        <!-- CSRF Token for AJAX requests -->
        <script>
            window.csrfToken = '{{ csrf_token() }}';
        </script>

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            <!-- Footer -->
            <footer class="footer">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div class="footer-links">
                            <h4>ShowLottie</h4>
                            <p class="mt-2 text-sm text-gray-300">
                                Create, edit, collaborate on and implement lightweight Lottie animations across websites, apps, socials and more.
                            </p>
                        </div>

                        <div class="footer-links">
                            <h4>Products</h4>
                            <ul>
                                <li><a href="#">Free Animations</a></li>
                                <li><a href="#">Marketplace</a></li>
                                <li><a href="#">Lottie Editor</a></li>
                                <li><a href="#">Lottie Creator</a></li>
                            </ul>
                        </div>

                        <div class="footer-links">
                            <h4>Resources</h4>
                            <ul>
                                <li><a href="#">Blog</a></li>
                                <li><a href="#">What is Lottie</a></li>
                                <li><a href="#">Community</a></li>
                                <li><a href="#">Developer Portal</a></li>
                            </ul>
                        </div>

                        <div class="footer-links">
                            <h4>Company</h4>
                            <ul>
                                <li><a href="#">About Us</a></li>
                                <li><a href="#">Careers</a></li>
                                <li><a href="#">Contact</a></li>
                                <li><a href="#">Privacy Policy</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="footer-bottom">
                        <p>&copy; {{ date('Y') }} ShowLottie. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </div>

        @livewireScripts
        @stack('scripts')
    </body>
</html>
