import{a as L,d as M,s as J,b as $}from"./firebase-xVbGI4Uy.js";import{o as lt,s as ut,c as ft,a as dt,d as pt,b as ge,u as ht,g as mt,e as Se,f as yt,h as bt,r as oe,i as Re,j as Et,l as wt}from"./index.esm2017-Bi9TsIr0.js";function je(e,t){return function(){return e.apply(t,arguments)}}const{toString:gt}=Object.prototype,{getPrototypeOf:ye}=Object,{iterator:Z,toStringTag:qe}=Symbol,Y=(e=>t=>{const n=gt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),F=e=>(e=e.toLowerCase(),t=>Y(t)===e),ee=e=>t=>typeof t===e,{isArray:k}=Array,H=ee("undefined");function St(e){return e!==null&&!H(e)&&e.constructor!==null&&!H(e.constructor)&&O(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Me=F("ArrayBuffer");function Rt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Me(e.buffer),t}const At=ee("string"),O=ee("function"),He=ee("number"),te=e=>e!==null&&typeof e=="object",Tt=e=>e===!0||e===!1,K=e=>{if(Y(e)!=="object")return!1;const t=ye(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(qe in e)&&!(Z in e)},Ot=F("Date"),Ct=F("File"),_t=F("Blob"),Ft=F("FileList"),xt=e=>te(e)&&O(e.pipe),Pt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||O(e.append)&&((t=Y(e))==="formdata"||t==="object"&&O(e.toString)&&e.toString()==="[object FormData]"))},Nt=F("URLSearchParams"),[Dt,Bt,Ut,It]=["ReadableStream","Request","Response","Headers"].map(F),Lt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function v(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),k(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function ve(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const B=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ze=e=>!H(e)&&e!==B;function le(){const{caseless:e}=ze(this)&&this||{},t={},n=(r,s)=>{const o=e&&ve(t,s)||s;K(t[o])&&K(r)?t[o]=le(t[o],r):K(r)?t[o]=le({},r):k(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&v(arguments[r],n);return t}const kt=(e,t,n,{allOwnKeys:r}={})=>(v(t,(s,o)=>{n&&O(s)?e[o]=je(s,n):e[o]=s},{allOwnKeys:r}),e),jt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),qt=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Mt=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&ye(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Ht=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},vt=e=>{if(!e)return null;if(k(e))return e;let t=e.length;if(!He(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},zt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ye(Uint8Array)),Vt=(e,t)=>{const r=(e&&e[Z]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},$t=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Jt=F("HTMLFormElement"),Kt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Ae=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Wt=F("RegExp"),Ve=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};v(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Xt=e=>{Ve(e,(t,n)=>{if(O(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(O(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Gt=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return k(e)?r(e):r(String(e).split(t)),n},Qt=()=>{},Zt=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Yt(e){return!!(e&&O(e.append)&&e[qe]==="FormData"&&e[Z])}const en=e=>{const t=new Array(10),n=(r,s)=>{if(te(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=k(r)?[]:{};return v(r,(i,c)=>{const f=n(i,s+1);!H(f)&&(o[c]=f)}),t[s]=void 0,o}}return r};return n(e,0)},tn=F("AsyncFunction"),nn=e=>e&&(te(e)||O(e))&&O(e.then)&&O(e.catch),$e=((e,t)=>e?setImmediate:t?((n,r)=>(B.addEventListener("message",({source:s,data:o})=>{s===B&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),B.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",O(B.postMessage)),rn=typeof queueMicrotask<"u"?queueMicrotask.bind(B):typeof process<"u"&&process.nextTick||$e,sn=e=>e!=null&&O(e[Z]),a={isArray:k,isArrayBuffer:Me,isBuffer:St,isFormData:Pt,isArrayBufferView:Rt,isString:At,isNumber:He,isBoolean:Tt,isObject:te,isPlainObject:K,isReadableStream:Dt,isRequest:Bt,isResponse:Ut,isHeaders:It,isUndefined:H,isDate:Ot,isFile:Ct,isBlob:_t,isRegExp:Wt,isFunction:O,isStream:xt,isURLSearchParams:Nt,isTypedArray:zt,isFileList:Ft,forEach:v,merge:le,extend:kt,trim:Lt,stripBOM:jt,inherits:qt,toFlatObject:Mt,kindOf:Y,kindOfTest:F,endsWith:Ht,toArray:vt,forEachEntry:Vt,matchAll:$t,isHTMLForm:Jt,hasOwnProperty:Ae,hasOwnProp:Ae,reduceDescriptors:Ve,freezeMethods:Xt,toObjectSet:Gt,toCamelCase:Kt,noop:Qt,toFiniteNumber:Zt,findKey:ve,global:B,isContextDefined:ze,isSpecCompliantForm:Yt,toJSONObject:en,isAsyncFn:tn,isThenable:nn,setImmediate:$e,asap:rn,isIterable:sn};function m(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(m,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Je=m.prototype,Ke={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ke[e]={value:e}});Object.defineProperties(m,Ke);Object.defineProperty(Je,"isAxiosError",{value:!0});m.from=(e,t,n,r,s,o)=>{const i=Object.create(Je);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),m.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const on=null;function ue(e){return a.isPlainObject(e)||a.isArray(e)}function We(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Te(e,t,n){return e?e.concat(t).map(function(s,o){return s=We(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function an(e){return a.isArray(e)&&!e.some(ue)}const cn=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function ne(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,h){return!a.isUndefined(h[y])});const r=n.metaTokens,s=n.visitor||u,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function l(p){if(p===null)return"";if(a.isDate(p))return p.toISOString();if(!f&&a.isBlob(p))throw new m("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(p)||a.isTypedArray(p)?f&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function u(p,y,h){let E=p;if(p&&!h&&typeof p=="object"){if(a.endsWith(y,"{}"))y=r?y:y.slice(0,-2),p=JSON.stringify(p);else if(a.isArray(p)&&an(p)||(a.isFileList(p)||a.endsWith(y,"[]"))&&(E=a.toArray(p)))return y=We(y),E.forEach(function(R,P){!(a.isUndefined(R)||R===null)&&t.append(i===!0?Te([y],P,o):i===null?y:y+"[]",l(R))}),!1}return ue(p)?!0:(t.append(Te(h,y,o),l(p)),!1)}const d=[],b=Object.assign(cn,{defaultVisitor:u,convertValue:l,isVisitable:ue});function g(p,y){if(!a.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(p),a.forEach(p,function(E,S){(!(a.isUndefined(E)||E===null)&&s.call(t,E,a.isString(S)?S.trim():S,y,b))===!0&&g(E,y?y.concat(S):[S])}),d.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Oe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function be(e,t){this._pairs=[],e&&ne(e,this,t)}const Xe=be.prototype;Xe.append=function(t,n){this._pairs.push([t,n])};Xe.toString=function(t){const n=t?function(r){return t.call(this,r,Oe)}:Oe;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function ln(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ge(e,t,n){if(!t)return e;const r=n&&n.encode||ln;a.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=a.isURLSearchParams(t)?t.toString():new be(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Ce{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Qe={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},un=typeof URLSearchParams<"u"?URLSearchParams:be,fn=typeof FormData<"u"?FormData:null,dn=typeof Blob<"u"?Blob:null,pn={isBrowser:!0,classes:{URLSearchParams:un,FormData:fn,Blob:dn},protocols:["http","https","file","blob","url","data"]},Ee=typeof window<"u"&&typeof document<"u",fe=typeof navigator=="object"&&navigator||void 0,hn=Ee&&(!fe||["ReactNative","NativeScript","NS"].indexOf(fe.product)<0),mn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",yn=Ee&&window.location.href||"http://localhost",bn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ee,hasStandardBrowserEnv:hn,hasStandardBrowserWebWorkerEnv:mn,navigator:fe,origin:yn},Symbol.toStringTag,{value:"Module"})),A={...bn,...pn};function En(e,t){return ne(e,new A.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return A.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function wn(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function gn(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Ze(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=n.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&a.isArray(s[i])&&(s[i]=gn(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,s)=>{t(wn(r),s,n,0)}),n}return null}function Sn(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const z={transitional:Qe,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(Ze(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return En(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return ne(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Sn(t)):t}],transformResponse:[function(t){const n=this.transitional||z.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?m.from(c,m.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:A.classes.FormData,Blob:A.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{z.headers[e]={}});const Rn=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),An=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Rn[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},_e=Symbol("internals");function q(e){return e&&String(e).trim().toLowerCase()}function W(e){return e===!1||e==null?e:a.isArray(e)?e.map(W):String(e)}function Tn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const On=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ie(e,t,n,r,s){if(a.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function Cn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function _n(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let C=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,f,l){const u=q(f);if(!u)throw new Error("header name must be a non-empty string");const d=a.findKey(s,u);(!d||s[d]===void 0||l===!0||l===void 0&&s[d]!==!1)&&(s[d||f]=W(c))}const i=(c,f)=>a.forEach(c,(l,u)=>o(l,u,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!On(t))i(An(t),n);else if(a.isObject(t)&&a.isIterable(t)){let c={},f,l;for(const u of t){if(!a.isArray(u))throw TypeError("Object iterator must return a key-value pair");c[l=u[0]]=(f=c[l])?a.isArray(f)?[...f,u[1]]:[f,u[1]]:u[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=q(t),t){const r=a.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Tn(s);if(a.isFunction(n))return n.call(this,s,r);if(a.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=q(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ie(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=q(i),i){const c=a.findKey(r,i);c&&(!n||ie(r,r[c],c,n))&&(delete r[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||ie(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return a.forEach(this,(s,o)=>{const i=a.findKey(r,o);if(i){n[i]=W(s),delete n[o];return}const c=t?Cn(o):String(o).trim();c!==o&&delete n[o],n[c]=W(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[_e]=this[_e]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=q(i);r[c]||(_n(s,i),r[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};C.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(C.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(C);function ae(e,t){const n=this||z,r=t||n,s=C.from(r.headers);let o=r.data;return a.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Ye(e){return!!(e&&e.__CANCEL__)}function j(e,t,n){m.call(this,e??"canceled",m.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(j,m,{__CANCEL__:!0});function et(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new m("Request failed with status code "+n.status,[m.ERR_BAD_REQUEST,m.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Fn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function xn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const l=Date.now(),u=r[o];i||(i=l),n[s]=f,r[s]=l;let d=o,b=0;for(;d!==s;)b+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-i<t)return;const g=u&&l-u;return g?Math.round(b*1e3/g):void 0}}function Pn(e,t){let n=0,r=1e3/t,s,o;const i=(l,u=Date.now())=>{n=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),d=u-n;d>=r?i(l,u):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const G=(e,t,n=3)=>{let r=0;const s=xn(50,250);return Pn(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-r,l=s(f),u=i<=c;r=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:l||void 0,estimated:l&&c&&u?(c-i)/l:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(d)},n)},Fe=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},xe=e=>(...t)=>a.asap(()=>e(...t)),Nn=A.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,A.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(A.origin),A.navigator&&/(msie|trident)/i.test(A.navigator.userAgent)):()=>!0,Dn=A.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Bn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Un(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function tt(e,t,n){let r=!Bn(t);return e&&(r||n==!1)?Un(e,t):t}const Pe=e=>e instanceof C?{...e}:e;function I(e,t){t=t||{};const n={};function r(l,u,d,b){return a.isPlainObject(l)&&a.isPlainObject(u)?a.merge.call({caseless:b},l,u):a.isPlainObject(u)?a.merge({},u):a.isArray(u)?u.slice():u}function s(l,u,d,b){if(a.isUndefined(u)){if(!a.isUndefined(l))return r(void 0,l,d,b)}else return r(l,u,d,b)}function o(l,u){if(!a.isUndefined(u))return r(void 0,u)}function i(l,u){if(a.isUndefined(u)){if(!a.isUndefined(l))return r(void 0,l)}else return r(void 0,u)}function c(l,u,d){if(d in t)return r(l,u);if(d in e)return r(void 0,l)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(l,u,d)=>s(Pe(l),Pe(u),d,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(u){const d=f[u]||s,b=d(e[u],t[u],u);a.isUndefined(b)&&d!==c||(n[u]=b)}),n}const nt=e=>{const t=I({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=C.from(i),t.url=Ge(tt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(A.hasStandardBrowserEnv||A.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[l,...u]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...u].join("; "))}}if(A.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&Nn(t.url))){const l=s&&o&&Dn.read(o);l&&i.set(s,l)}return t},In=typeof XMLHttpRequest<"u",Ln=In&&function(e){return new Promise(function(n,r){const s=nt(e);let o=s.data;const i=C.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:l}=s,u,d,b,g,p;function y(){g&&g(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let h=new XMLHttpRequest;h.open(s.method.toUpperCase(),s.url,!0),h.timeout=s.timeout;function E(){if(!h)return;const R=C.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),T={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:R,config:e,request:h};et(function(D){n(D),y()},function(D){r(D),y()},T),h=null}"onloadend"in h?h.onloadend=E:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(E)},h.onabort=function(){h&&(r(new m("Request aborted",m.ECONNABORTED,e,h)),h=null)},h.onerror=function(){r(new m("Network Error",m.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let P=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const T=s.transitional||Qe;s.timeoutErrorMessage&&(P=s.timeoutErrorMessage),r(new m(P,T.clarifyTimeoutError?m.ETIMEDOUT:m.ECONNABORTED,e,h)),h=null},o===void 0&&i.setContentType(null),"setRequestHeader"in h&&a.forEach(i.toJSON(),function(P,T){h.setRequestHeader(T,P)}),a.isUndefined(s.withCredentials)||(h.withCredentials=!!s.withCredentials),c&&c!=="json"&&(h.responseType=s.responseType),l&&([b,p]=G(l,!0),h.addEventListener("progress",b)),f&&h.upload&&([d,g]=G(f),h.upload.addEventListener("progress",d),h.upload.addEventListener("loadend",g)),(s.cancelToken||s.signal)&&(u=R=>{h&&(r(!R||R.type?new j(null,e,h):R),h.abort(),h=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const S=Fn(s.url);if(S&&A.protocols.indexOf(S)===-1){r(new m("Unsupported protocol "+S+":",m.ERR_BAD_REQUEST,e));return}h.send(o||null)})},kn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(l){if(!s){s=!0,c();const u=l instanceof Error?l:this.reason;r.abort(u instanceof m?u:new j(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new m(`timeout ${t} of ms exceeded`,m.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),e=null)};e.forEach(l=>l.addEventListener("abort",o));const{signal:f}=r;return f.unsubscribe=()=>a.asap(c),f}},jn=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},qn=async function*(e,t){for await(const n of Mn(e))yield*jn(n,t)},Mn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Ne=(e,t,n,r)=>{const s=qn(e,t);let o=0,i,c=f=>{i||(i=!0,r&&r(f))};return new ReadableStream({async pull(f){try{const{done:l,value:u}=await s.next();if(l){c(),f.close();return}let d=u.byteLength;if(n){let b=o+=d;n(b)}f.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},re=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",rt=re&&typeof ReadableStream=="function",Hn=re&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),st=(e,...t)=>{try{return!!e(...t)}catch{return!1}},vn=rt&&st(()=>{let e=!1;const t=new Request(A.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),De=64*1024,de=rt&&st(()=>a.isReadableStream(new Response("").body)),Q={stream:de&&(e=>e.body)};re&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Q[t]&&(Q[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new m(`Response type '${t}' is not supported`,m.ERR_NOT_SUPPORT,r)})})})(new Response);const zn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(A.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await Hn(e)).byteLength},Vn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??zn(t)},$n=re&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:b}=nt(e);l=l?(l+"").toLowerCase():"text";let g=kn([s,o&&o.toAbortSignal()],i),p;const y=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let h;try{if(f&&vn&&n!=="get"&&n!=="head"&&(h=await Vn(u,r))!==0){let T=new Request(t,{method:"POST",body:r,duplex:"half"}),N;if(a.isFormData(r)&&(N=T.headers.get("content-type"))&&u.setContentType(N),T.body){const[D,V]=Fe(h,G(xe(f)));r=Ne(T.body,De,D,V)}}a.isString(d)||(d=d?"include":"omit");const E="credentials"in Request.prototype;p=new Request(t,{...b,signal:g,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:E?d:void 0});let S=await fetch(p);const R=de&&(l==="stream"||l==="response");if(de&&(c||R&&y)){const T={};["status","statusText","headers"].forEach(we=>{T[we]=S[we]});const N=a.toFiniteNumber(S.headers.get("content-length")),[D,V]=c&&Fe(N,G(xe(c),!0))||[];S=new Response(Ne(S.body,De,D,()=>{V&&V(),y&&y()}),T)}l=l||"text";let P=await Q[a.findKey(Q,l)||"text"](S,e);return!R&&y&&y(),await new Promise((T,N)=>{et(T,N,{data:P,headers:C.from(S.headers),status:S.status,statusText:S.statusText,config:e,request:p})})}catch(E){throw y&&y(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new m("Network Error",m.ERR_NETWORK,e,p),{cause:E.cause||E}):m.from(E,E&&E.code,e,p)}}),pe={http:on,xhr:Ln,fetch:$n};a.forEach(pe,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Be=e=>`- ${e}`,Jn=e=>a.isFunction(e)||e===null||e===!1,ot={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Jn(n)&&(r=pe[(i=String(n)).toLowerCase()],r===void 0))throw new m(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Be).join(`
`):" "+Be(o[0]):"as no adapter specified";throw new m("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:pe};function ce(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new j(null,e)}function Ue(e){return ce(e),e.headers=C.from(e.headers),e.data=ae.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ot.getAdapter(e.adapter||z.adapter)(e).then(function(r){return ce(e),r.data=ae.call(e,e.transformResponse,r),r.headers=C.from(r.headers),r},function(r){return Ye(r)||(ce(e),r&&r.response&&(r.response.data=ae.call(e,e.transformResponse,r.response),r.response.headers=C.from(r.response.headers))),Promise.reject(r)})}const it="1.9.0",se={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{se[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ie={};se.transitional=function(t,n,r){function s(o,i){return"[Axios v"+it+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new m(s(i," has been removed"+(n?" in "+n:"")),m.ERR_DEPRECATED);return n&&!Ie[i]&&(Ie[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};se.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Kn(e,t,n){if(typeof e!="object")throw new m("options must be an object",m.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new m("option "+o+" must be "+f,m.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new m("Unknown option "+o,m.ERR_BAD_OPTION)}}const X={assertOptions:Kn,validators:se},x=X.validators;let U=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Ce,response:new Ce}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=I(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&X.assertOptions(r,{silentJSONParsing:x.transitional(x.boolean),forcedJSONParsing:x.transitional(x.boolean),clarifyTimeoutError:x.transitional(x.boolean)},!1),s!=null&&(a.isFunction(s)?n.paramsSerializer={serialize:s}:X.assertOptions(s,{encode:x.function,serialize:x.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),X.assertOptions(n,{baseUrl:x.spelling("baseURL"),withXsrfToken:x.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=C.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(f=f&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,d=0,b;if(!f){const p=[Ue.bind(this),void 0];for(p.unshift.apply(p,c),p.push.apply(p,l),b=p.length,u=Promise.resolve(n);d<b;)u=u.then(p[d++],p[d++]);return u}b=c.length;let g=n;for(d=0;d<b;){const p=c[d++],y=c[d++];try{g=p(g)}catch(h){y.call(this,h);break}}try{u=Ue.call(this,g)}catch(p){return Promise.reject(p)}for(d=0,b=l.length;d<b;)u=u.then(l[d++],l[d++]);return u}getUri(t){t=I(this.defaults,t);const n=tt(t.baseURL,t.url,t.allowAbsoluteUrls);return Ge(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){U.prototype[t]=function(n,r){return this.request(I(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(I(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}U.prototype[t]=n(),U.prototype[t+"Form"]=n(!0)});let Wn=class at{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new j(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new at(function(s){t=s}),cancel:t}}};function Xn(e){return function(n){return e.apply(null,n)}}function Gn(e){return a.isObject(e)&&e.isAxiosError===!0}const he={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(he).forEach(([e,t])=>{he[t]=e});function ct(e){const t=new U(e),n=je(U.prototype.request,t);return a.extend(n,U.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return ct(I(e,s))},n}const w=ct(z);w.Axios=U;w.CanceledError=j;w.CancelToken=Wn;w.isCancel=Ye;w.VERSION=it;w.toFormData=ne;w.AxiosError=m;w.Cancel=w.CanceledError;w.all=function(t){return Promise.all(t)};w.spread=Xn;w.isAxiosError=Gn;w.mergeConfig=I;w.AxiosHeaders=C;w.formToJSON=e=>Ze(a.isHTMLForm(e)?new FormData(e):e);w.getAdapter=ot.getAdapter;w.HttpStatusCode=he;w.default=w;const{Axios:ar,AxiosError:cr,CanceledError:lr,isCancel:ur,CancelToken:fr,VERSION:dr,all:pr,Cancel:hr,isAxiosError:mr,spread:yr,toFormData:br,AxiosHeaders:Er,HttpStatusCode:wr,formToJSON:gr,getAdapter:Sr,mergeConfig:Rr}=w;window.axios=w;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const _=()=>L!==null&&M!==null&&J!==null,Qn={signIn:async(e,t)=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,user:(await dt(L,e,t)).user}}catch(n){return{success:!1,error:n.message}}},register:async(e,t)=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,user:(await ft(L,e,t)).user}}catch(n){return{success:!1,error:n.message}}},signOut:async()=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return await ut(L),{success:!0}}catch(e){return{success:!1,error:e.message}}},onAuthStateChanged:e=>_()?lt(L,e):(console.warn("Firebase not initialized, auth state listener not available"),()=>{}),getCurrentUser:()=>_()?L.currentUser:null},Zn={addDocument:async(e,t)=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,id:(await yt(Se(M,e),t)).id}}catch(n){return{success:!1,error:n.message}}},getDocuments:async e=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{const t=await mt(Se(M,e)),n=[];return t.forEach(r=>{n.push({id:r.id,...r.data()})}),{success:!0,data:n}}catch(t){return{success:!1,error:t.message}}},updateDocument:async(e,t,n)=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{const r=ge(M,e,t);return await ht(r,n),{success:!0}}catch(r){return{success:!1,error:r.message}}},deleteDocument:async(e,t)=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return await pt(ge(M,e,t)),{success:!0}}catch(n){return{success:!1,error:n.message}}}},Yn={uploadFile:async(e,t)=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{const n=oe(J,t),r=await Et(n,e);return{success:!0,url:await Re(r.ref),path:t}}catch(n){return{success:!1,error:n.message}}},getDownloadURL:async e=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return{success:!0,url:await Re(oe(J,e))}}catch(t){return{success:!1,error:t.message}}},deleteFile:async e=>{if(!_())return{success:!1,error:"Firebase not initialized"};try{return await bt(oe(J,e)),{success:!0}}catch(t){return{success:!1,error:t.message}}}},me={logEvent:async(e,t={})=>{try{let n=0;const r=10;for(;!$&&n<r;)await new Promise(s=>setTimeout(s,100)),n++;return $?(await wt($,e,t),{success:!0}):(console.warn("Firebase Analytics not initialized after waiting"),{success:!1,error:"Analytics not initialized"})}catch(n){return console.error("Analytics error:",n),{success:!1,error:n.message}}},logPageView:async e=>me.logEvent("page_view",{page_name:e}),logUserAction:async(e,t={})=>me.logEvent("user_action",{action:e,...t}),isAvailable:()=>$!==null};window.firebaseAuth=Qn;window.firebaseDB=Zn;window.firebaseStorage=Yn;window.firebaseAnalytics=me;const Le={BASE_URL:"/build/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_APP_NAME:"ShowLottie",VITE_FIREBASE_API_KEY:"AIzaSyBN68eIVPPp6S7hAIXZujXmj-0v87S0KA8",VITE_FIREBASE_APP_ID:"1:29289879674:web:dc10ec62966f9be1e583c3",VITE_FIREBASE_AUTH_DOMAIN:"lottie-cfb42.firebaseapp.com",VITE_FIREBASE_MEASUREMENT_ID:"G-PE7G8QLBWH",VITE_FIREBASE_MESSAGING_SENDER_ID:"29289879674",VITE_FIREBASE_PROJECT_ID:"lottie-cfb42",VITE_FIREBASE_STORAGE_BUCKET:"lottie-cfb42.firebasestorage.app"};console.log("🔍 Checking Firebase Configuration...");const er=()=>{const e=["VITE_FIREBASE_API_KEY","VITE_FIREBASE_AUTH_DOMAIN","VITE_FIREBASE_PROJECT_ID","VITE_FIREBASE_STORAGE_BUCKET","VITE_FIREBASE_MESSAGING_SENDER_ID","VITE_FIREBASE_APP_ID"],t=["VITE_FIREBASE_MEASUREMENT_ID"];console.log(`
📋 Environment Variables Check:`);let n=!0;return e.forEach(r=>{const s=Le[r];s?console.log(`✅ ${r}: ${s.substring(0,10)}...`):(console.log(`❌ ${r}: Missing`),n=!1)}),t.forEach(r=>{const s=Le[r];console.log(s?`✅ ${r}: ${s.substring(0,10)}...`:`⚠️  ${r}: Missing (optional for analytics)`)}),n},tr=()=>{if(console.log(`
🔧 Firebase Services Check:`),typeof window<"u"){const e={"Firebase Auth":window.firebaseAuth,"Firebase DB":window.firebaseDB,"Firebase Storage":window.firebaseStorage,"Firebase Analytics":window.firebaseAnalytics};Object.entries(e).forEach(([t,n])=>{console.log(n?`✅ ${t}: Available`:`❌ ${t}: Not available`)})}else console.log("⚠️  Not in browser environment")},nr=()=>{console.log(`
🚨 Error Check:`);const e=console.error,t=[];console.error=(...n)=>{const r=n.join(" ");(r.includes("firebase")||r.includes("Firebase"))&&t.push(r),e.apply(console,n)},setTimeout(()=>{t.length>0?(console.log("❌ Firebase errors detected:"),t.forEach(n=>{console.log(`   - ${n}`)})):console.log("✅ No Firebase errors detected"),console.error=e},2e3)},ke=()=>{console.log(`🚀 Starting Firebase Configuration Check...
`);const e=er();tr(),nr(),setTimeout(()=>{console.log(`
📊 Summary:`),e?console.log("✅ Environment variables are properly configured"):(console.log("❌ Missing required environment variables"),console.log("💡 Please check your .env file and ensure all required Firebase variables are set")),console.log(`
🔧 Next Steps:`),console.log("1. If environment variables are missing, add them to your .env file"),console.log("2. Restart your development server after changing .env"),console.log('3. Run "npm run build" to rebuild assets'),console.log("4. Visit /firebase-test to test the integration")},3e3)};typeof window<"u"&&(document.readyState==="loading"?document.addEventListener("DOMContentLoaded",ke):ke());document.addEventListener("DOMContentLoaded",function(){window.executeRecaptchaOnSubmit=function(e,t="submit"){return new Promise((n,r)=>{var o;if(typeof grecaptcha>"u"){console.error("reCAPTCHA not loaded"),r(new Error("reCAPTCHA not loaded"));return}const s=e.dataset.recaptchaSiteKey||((o=document.querySelector("[data-recaptcha-site-key]"))==null?void 0:o.dataset.recaptchaSiteKey);if(!s){console.error("reCAPTCHA site key not found"),r(new Error("reCAPTCHA site key not found"));return}grecaptcha.execute(s,{action:t}).then(i=>{let c=e.querySelector('input[name="g-recaptcha-response"]');c||(c=document.createElement("input"),c.type="hidden",c.name="g-recaptcha-response",e.appendChild(c)),c.value=i,window.dispatchEvent(new CustomEvent("recaptcha-token-received",{detail:{token:i}})),n(i)}).catch(i=>{console.error("reCAPTCHA execution failed:",i),r(i)})})},document.addEventListener("submit",function(e){const t=e.target;if(t.dataset.recaptchaEnabled==="true"){e.preventDefault();const n=t.dataset.recaptchaAction||"submit";executeRecaptchaOnSubmit(t,n).then(()=>{t.submit()}).catch(r=>{console.error("reCAPTCHA failed:",r);const s=document.createElement("div");s.className="text-red-600 text-sm mt-2",s.textContent="Security verification failed. Please try again.";const o=t.querySelector(".text-red-600");o&&o.remove(),t.appendChild(s)})}}),window.executeRecaptcha=function(e="submit"){const t=document.querySelector('[data-recaptcha-enabled="true"]');return t?executeRecaptchaOnSubmit(t,e):Promise.reject(new Error("No reCAPTCHA enabled form found"))}});
