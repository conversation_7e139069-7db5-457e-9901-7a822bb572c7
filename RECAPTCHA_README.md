# Google reCAPTCHA v3 Implementation

This project now uses Google reCAPTCHA v3 for secure, user-friendly bot protection on authentication forms.

## Features

- **Google reCAPTCHA v3**: Invisible, score-based protection
- **Server-side validation**: Secure token verification
- **Score-based blocking**: Configurable threshold for different actions
- **Livewire integration**: Seamless integration with Livewire components
- **Comprehensive testing**: Full test coverage with proper mocking

## Setup

### 1. Get reCAPTCHA Keys

1. Go to [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Create a new site
3. Choose **reCAPTCHA v3**
4. Add your domain(s)
5. Copy the **Site Key** and **Secret Key**

### 2. Configure Environment Variables

Add these to your `.env` file:

```env
RECAPTCHA_SITE_KEY=your_site_key_here
RECAPTCHA_SECRET_KEY=your_secret_key_here
RECAPTCHA_SCORE_THRESHOLD=0.5
```

### 3. Configuration Options

The reCAPTCHA system can be configured via `config/services.php`:

```php
'recaptcha' => [
    'site_key' => env('RECAPTCHA_SITE_KEY'),
    'secret_key' => env('RECAPTCHA_SECRET_KEY'),
    'score_threshold' => env('RECAPTCHA_SCORE_THRESHOLD', 0.5),
],
```

## How It Works

### Frontend (reCAPTCHA v3)
1. **Invisible Protection**: reCAPTCHA v3 runs in the background
2. **Score-based**: Returns a score (0.0 to 1.0) based on user behavior
3. **Action-based**: Different actions can have different thresholds
4. **No User Interaction**: Completely invisible to users

### Backend (Laravel)
1. **Token Validation**: Server validates the reCAPTCHA token
2. **Score Checking**: Verifies the score meets the threshold
3. **Error Handling**: Comprehensive error handling and logging

## Components

### GoogleRecaptchaService
- **Location**: `app/Services/GoogleRecaptchaService.php`
- **Purpose**: Server-side token validation
- **Features**:
  - Token verification with Google's API
  - Score threshold checking
  - Comprehensive error handling
  - Logging for debugging

### Livewire Component
- **Location**: `resources/views/livewire/components/google-recaptcha.blade.php`
- **Purpose**: Frontend integration
- **Features**:
  - Automatic script loading
  - Token generation and management
  - Error display
  - Alpine.js integration

## Usage

### In Forms

The reCAPTCHA component is automatically included in:
- Registration form (`resources/views/livewire/pages/auth/register.blade.php`)
- Login form (`resources/views/livewire/pages/auth/login.blade.php`)

### Adding to Other Forms

To add reCAPTCHA to other forms:

1. **Add the component**:
```blade
<livewire:components.google-recaptcha />
```

2. **Add validation**:
```php
#[Validate('required|string')]
public string $recaptchaToken = '';
```

3. **Validate in submit method**:
```php
$recaptchaService = new GoogleRecaptchaService();
$result = $recaptchaService->verify($this->recaptchaToken);

if (!$result['success'] || !$recaptchaService->isScoreAcceptable($result['score'])) {
    $this->addError('recaptcha', 'reCAPTCHA verification failed');
    return;
}
```

## Score Thresholds

### Recommended Thresholds
- **0.9+**: Very strict (high security, may block legitimate users)
- **0.7-0.8**: Strict (good security, minimal false positives)
- **0.5-0.6**: Moderate (balanced security and usability)
- **0.3-0.4**: Lenient (lower security, better user experience)

### Action-Specific Thresholds

You can implement different thresholds for different actions:

```php
// For registration (stricter)
if (!$recaptchaService->isScoreAcceptable($result['score'], 0.7)) {
    // Handle low score
}

// For login (moderate)
if (!$recaptchaService->isScoreAcceptable($result['score'], 0.5)) {
    // Handle low score
}
```

## Testing

### Running Tests
```bash
php artisan test --filter=Recaptcha
```

### Test Coverage
- Registration with valid reCAPTCHA
- Registration with invalid reCAPTCHA
- Login with valid reCAPTCHA
- Login with invalid reCAPTCHA
- Score threshold validation

### Mocking in Tests
```php
$this->mock(\App\Services\GoogleRecaptchaService::class, function ($mock) {
    $mock->shouldReceive('verify')->andReturn([
        'success' => true,
        'score' => 0.9,
        'action' => 'submit',
        'challenge_ts' => now()->toISOString(),
        'hostname' => 'localhost',
        'error_codes' => []
    ]);
    $mock->shouldReceive('isScoreAcceptable')->andReturn(true);
});
```

## Security Features

### 1. Server-Side Validation
- All tokens validated server-side
- No client-side bypass possible
- Secure secret key handling

### 2. Score-Based Protection
- Adaptive protection based on user behavior
- Configurable thresholds
- Action-specific scoring

### 3. Comprehensive Logging
- Failed verification attempts logged
- Error details captured for debugging
- Performance monitoring

### 4. Error Handling
- Graceful degradation if reCAPTCHA is down
- Clear error messages for users
- Fallback mechanisms

## Troubleshooting

### reCAPTCHA Not Loading
- Check if `RECAPTCHA_SITE_KEY` is set in `.env`
- Verify domain is added to reCAPTCHA admin console
- Check browser console for JavaScript errors

### Validation Failing
- Verify `RECAPTCHA_SECRET_KEY` is correct
- Check server logs for detailed error messages
- Ensure domain matches reCAPTCHA configuration

### Score Too Low
- Adjust `RECAPTCHA_SCORE_THRESHOLD` in `.env`
- Consider different thresholds for different actions
- Monitor legitimate user blocking

### Development/Testing
For development, you can temporarily disable reCAPTCHA:
```php
// In your service or controller
if (app()->environment('local')) {
    return ['success' => true, 'score' => 1.0];
}
```

## Best Practices

### 1. Environment-Specific Configuration
```env
# Production
RECAPTCHA_SCORE_THRESHOLD=0.7

# Staging
RECAPTCHA_SCORE_THRESHOLD=0.5

# Development
RECAPTCHA_SCORE_THRESHOLD=0.3
```

### 2. Action-Specific Scoring
```php
// Different actions can have different thresholds
$thresholds = [
    'register' => 0.7,
    'login' => 0.5,
    'contact' => 0.3,
];

$threshold = $thresholds[$action] ?? 0.5;
```

### 3. Monitoring and Analytics
- Log reCAPTCHA scores for analysis
- Monitor false positive rates
- Track user experience metrics

### 4. Fallback Mechanisms
- Graceful degradation if reCAPTCHA fails
- Alternative verification methods
- User-friendly error messages

## Migration from Mathematical Captcha

The mathematical captcha has been completely replaced with Google reCAPTCHA v3. Benefits:

- **Better Security**: Advanced bot detection
- **Better UX**: Invisible to users
- **Better Performance**: No server-side generation
- **Better Analytics**: Detailed scoring and insights
- **Industry Standard**: Google's battle-tested solution 