import{k as o,m as l,n as s,p as r,q as c,l as d}from"./index.esm2017-Bi9TsIr0.js";const e={apiKey:"AIzaSyBN68eIVPPp6S7hAIXZujXmj-0v87S0KA8",authDomain:"lottie-cfb42.firebaseapp.com",projectId:"lottie-cfb42",storageBucket:"lottie-cfb42.firebasestorage.app",messagingSenderId:"29289879674",appId:"1:29289879674:web:dc10ec62966f9be1e583c3",measurementId:"G-PE7G8QLBWH"},E=()=>e.apiKey&&e.authDomain&&e.projectId&&e.storageBucket&&e.messagingSenderId&&e.appId;let i=null,t=null,I=null,u=null,p=null;if(E())try{i=o(e),I=l(i),u=s(i),p=r(i),console.log("Firebase initialized successfully")}catch(a){console.error("Firebase initialization failed:",a)}else console.warn("Firebase configuration incomplete. Please check your environment variables."),console.warn("Required variables: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID");const n=()=>{if(typeof window<"u"&&i&&!t)try{t=c(i),d(t,"page_view"),console.log("Firebase Analytics initialized successfully")}catch(a){console.warn("Firebase Analytics initialization failed:",a)}};typeof window<"u"&&(document.readyState==="loading"?document.addEventListener("DOMContentLoaded",n):n());
