document.addEventListener("DOMContentLoaded",function(){window.executeRecaptchaOnSubmit=function(t,e="submit"){return new Promise((i,c)=>{var o;if(typeof grecaptcha>"u"){console.error("reCAPTCHA not loaded"),c(new Error("reCAPTCHA not loaded"));return}const n=t.dataset.recaptchaSiteKey||((o=document.querySelector("[data-recaptcha-site-key]"))==null?void 0:o.dataset.recaptchaSiteKey);if(!n){console.error("reCAPTCHA site key not found"),c(new Error("reCAPTCHA site key not found"));return}grecaptcha.execute(n,{action:e}).then(r=>{let a=t.querySelector('input[name="g-recaptcha-response"]');a||(a=document.createElement("input"),a.type="hidden",a.name="g-recaptcha-response",t.appendChild(a)),a.value=r,window.dispatchEvent(new CustomEvent("recaptcha-token-received",{detail:{token:r}})),i(r)}).catch(r=>{console.error("reCAPTCHA execution failed:",r),c(r)})})},document.addEventListener("submit",function(t){const e=t.target;if(e.dataset.recaptchaEnabled==="true"){t.preventDefault();const i=e.dataset.recaptchaAction||"submit";executeRecaptchaOnSubmit(e,i).then(()=>{e.submit()}).catch(c=>{console.error("reCAPTCHA failed:",c);const n=document.createElement("div");n.className="text-red-600 text-sm mt-2",n.textContent="Security verification failed. Please try again.";const o=e.querySelector(".text-red-600");o&&o.remove(),e.appendChild(n)})}}),window.executeRecaptcha=function(t="submit"){const e=document.querySelector('[data-recaptcha-enabled="true"]');return e?executeRecaptchaOnSubmit(e,t):Promise.reject(new Error("No reCAPTCHA enabled form found"))}});
