<?php

namespace Tests\Feature\Auth;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Volt\Volt;
use Tests\TestCase;

class RegistrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_registration_screen_can_be_rendered(): void
    {
        $response = $this->get('/register');

        $response
            ->assertOk()
            ->assertSeeVolt('pages.auth.register');
    }

    public function test_new_users_can_register(): void
    {
        // Mock reCAPTCHA service to return success
        $this->mock(\App\Services\GoogleRecaptchaService::class, function ($mock) {
            $mock->shouldReceive('verify')->andReturn([
                'success' => true,
                'score' => 0.9,
                'action' => 'submit',
                'challenge_ts' => now()->toISOString(),
                'hostname' => 'localhost',
                'error_codes' => []
            ]);
            $mock->shouldReceive('isScoreAcceptable')->andReturn(true);
        });

        $component = Volt::test('pages.auth.register')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password')
            ->set('recaptchaToken', 'test-token');

        $component->call('register');

        $component->assertRedirect(route('index', absolute: false));

        $this->assertAuthenticated();
    }

    public function test_registration_fails_with_invalid_recaptcha(): void
    {
        // Mock reCAPTCHA service to return failure
        $this->mock(\App\Services\GoogleRecaptchaService::class, function ($mock) {
            $mock->shouldReceive('verify')->andReturn([
                'success' => false,
                'score' => 0.0,
                'action' => 'submit',
                'challenge_ts' => now()->toISOString(),
                'hostname' => 'localhost',
                'error_codes' => ['invalid-input-response']
            ]);
        });

        $component = Volt::test('pages.auth.register')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password')
            ->set('recaptchaToken', 'invalid-token');

        $component->call('register');

        $component
            ->assertHasErrors(['recaptcha'])
            ->assertNoRedirect();

        $this->assertGuest();
    }
}
