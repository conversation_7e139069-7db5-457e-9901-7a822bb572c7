<?php

namespace Tests\Feature\Auth;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Volt\Volt;
use Tests\TestCase;

class RegistrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_registration_screen_can_be_rendered(): void
    {
        $response = $this->get('/register');

        $response
            ->assertOk()
            ->assertSeeVolt('pages.auth.register');
    }

    public function test_new_users_can_register(): void
    {
        // Mock the reCAPTCHA v2 response
        $this->app['request']->merge(['g-recaptcha-response' => 'test-token']);

        $component = Volt::test('pages.auth.register')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password');

        // Mock the file_get_contents call for reCAPTCHA verification
        $this->mock('alias:file_get_contents', function () {
            return json_encode(['success' => true]);
        });

        $component->call('register');

        $component->assertRedirect(route('index', absolute: false));

        $this->assertAuthenticated();
    }

    public function test_registration_fails_with_invalid_recaptcha(): void
    {
        // Mock the reCAPTCHA v2 response with invalid token
        $this->app['request']->merge(['g-recaptcha-response' => 'invalid-token']);

        $component = Volt::test('pages.auth.register')
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password');

        // Mock the file_get_contents call for reCAPTCHA verification to return failure
        $this->mock('alias:file_get_contents', function () {
            return json_encode(['success' => false]);
        });

        $component->call('register');

        $component
            ->assertHasErrors(['recaptcha'])
            ->assertNoRedirect();

        $this->assertGuest();
    }
}
